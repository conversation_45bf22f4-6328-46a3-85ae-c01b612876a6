import Script from "@/app/core/script";
import Step from "@/app/core/step";
import h from "@/app/helpers/all";

/**
 * Constants for test execution flags
 */
export const TEST_EXECUTION_FLAGS = {
  /** Execute the step */
  EXECUTE: "x",
  /** Execute the step and stop test execution */
  EXECUTE_AND_STOP: "z",
  /** Skip the step */
  SKIP: "",
} as const;

/**
 * Constants for script action names
 */
export const SCRIPT_ACTIONS = {
  RUN_SCRIPT: "Run Script",
  CALL_SCRIPT: "Call Script",
  START_SCRIPT: "Start Script",
  END_SCRIPT: "End Script",
  START_TEST: "Start Test",
  END_TEST: "End Test",
} as const;

/**
 * Interface defining the structure of a test step
 */
interface TestStep {
  /** Execution flag: 'x' = execute, 'z' = execute and stop, '' = skip */
  do?: string;
  /** The action to perform in this step */
  action?: string;
  /** The target element or identifier for the action */
  target?: string;
  /** Values or parameters for the action */
  values?: string;
  /** Row number from the source sheet */
  rowNumber?: string | number;
  /** Whether to take a screenshot after this step */
  screenshot?: boolean;
  /** Additional notes for this step */
  notes?: string;
  /** Title for grouping steps */
  title?: string;
  /** Subtitle for additional context */
  subTitle?: string;
}

/**
 * Interface defining the structure of test data
 */
interface TestData {
  /** Name of the sheet containing this test */
  sheet: string;
  /** Unique identifier for the test */
  id?: string | number;
  /** Flag indicating whether this test should run */
  runFlag?: string | null;
  /** Array of test steps to execute */
  steps: TestStep[];
  /** Additional dynamic properties from the test data */
  [key: string]: any;
}

/**
 * Test class represents a single test case with its steps and execution logic.
 * It handles script expansion, step processing, and Cypress test execution.
 */
export default class Test {
  /** Human-readable description generated from test properties */
  public description: string;
  /** Name of the sheet containing this test */
  public sheet: string;
  /** Unique identifier for the test */
  public id?: string | number;
  /** Flag indicating whether this test should run */
  public runFlag?: string | null;
  /** Array of processed test steps ready for execution */
  public steps: TestStep[];

  /**
   * Creates a new Test instance
   * @param test - The test data object containing all test properties
   */
  constructor(test: TestData) {
    // Copy all properties from the test data
    Object.assign(this, test);

    // Generate human-readable description
    this.description = this.generateDescription();

    // Process and expand steps (including script expansion)
    this.processSteps();
  }

  /**
   * Generates a human-readable description from test properties
   * Excludes internal properties and formats property names from camelCase
   * @returns Formatted description string
   */
  private generateDescription(): string {
    const excludedKeys = new Set(["description", "sheet", "runFlag", "steps"]);

    const description = Object.entries(this).reduce((result, [key, value]) => {
      if (!excludedKeys.has(key) && value !== undefined && value !== null) {
        const formattedKey = h.fromCamelCase(key);
        result += `${formattedKey}: ${value}\n`;
      }
      return result;
    }, "");

    return description.trim();
  }

  /**
   * Processes and expands test steps, including script expansion
   * Adds start/end markers and handles nested script calls
   */
  private processSteps(): void {
    // Expand any script calls within the steps
    this.steps = this.expandScriptSteps(this.steps);

    // Add test start marker
    this.steps.unshift({
      do: TEST_EXECUTION_FLAGS.EXECUTE,
      action: SCRIPT_ACTIONS.START_TEST,
    });

    // Add test end marker
    this.steps.push({
      do: TEST_EXECUTION_FLAGS.EXECUTE,
      action: SCRIPT_ACTIONS.END_TEST,
    });
  }

  /**
   * Expands script steps by replacing script calls with their actual steps
   * Handles nested script calls recursively and maintains proper execution flow
   * @param steps - Array of steps to process
   * @returns Array of expanded steps with scripts replaced by their content
   */
  private expandScriptSteps(steps: TestStep[]): TestStep[] {
    const expandedSteps = steps.reduce((acc: TestStep[], step: TestStep) => {
      // Check if this step is a script call
      if (
        step.do &&
        step.action &&
        (h.compareNormalizedStrings(step.action, SCRIPT_ACTIONS.RUN_SCRIPT) ||
          h.compareNormalizedStrings(step.action, SCRIPT_ACTIONS.CALL_SCRIPT))
      ) {
        try {
          // Create script instance and get its steps
          const foundScript = new Script(step);

          // Replace script parameters if values are provided
          if (step.values) {
            const runScriptValues = h
              .replaceEscapedCharactersAndQuotedStrings(step.values)
              .split(",");
            foundScript.replaceParams(runScriptValues);
          }

          let scriptSteps = [...foundScript.steps];

          // If the calling step has 'z' flag, apply it to the last script step
          if (
            step.do === TEST_EXECUTION_FLAGS.EXECUTE_AND_STOP &&
            scriptSteps.length > 0
          ) {
            scriptSteps[scriptSteps.length - 1].do =
              TEST_EXECUTION_FLAGS.EXECUTE_AND_STOP;
          }

          // Add script start marker
          scriptSteps.unshift({
            do: TEST_EXECUTION_FLAGS.EXECUTE,
            action: `${SCRIPT_ACTIONS.START_SCRIPT} - ${step.target}`,
            rowNumber: step.rowNumber,
          });

          // Add script end marker
          scriptSteps.push({
            do: TEST_EXECUTION_FLAGS.EXECUTE,
            action: `${SCRIPT_ACTIONS.END_SCRIPT} - ${step.target}`,
            rowNumber: step.rowNumber,
          });

          // Check for nested script calls and expand them recursively
          const hasNestedScripts = scriptSteps.some(
            (scriptStep) =>
              scriptStep.action &&
              (h.compareNormalizedStrings(
                scriptStep.action,
                SCRIPT_ACTIONS.RUN_SCRIPT
              ) ||
                h.compareNormalizedStrings(
                  scriptStep.action,
                  SCRIPT_ACTIONS.CALL_SCRIPT
                ))
          );

          if (hasNestedScripts) {
            scriptSteps = this.expandScriptSteps(scriptSteps);
          }

          acc.push(...scriptSteps);
        } catch (error) {
          // If script expansion fails, keep the original step and log the error
          console.error(`Failed to expand script: ${step.target}`, error);
          acc.push(step);
        }
      } else {
        // Regular step, add as-is
        acc.push(step);
      }
      return acc;
    }, []);

    return expandedSteps;
  }

  /**
   * Executes the test using Cypress framework
   * Creates a Cypress test case and runs all steps sequentially
   * Manages test state and tracks execution in the global SDT results
   */
  public run(): void {
    it(this.description, () => {
      try {
        // Set current test in global state
        Cypress.sdt.current.test = this;

        // Track this test as executed
        Cypress.sdt.results.executedTests.push(Cypress.sdt.current.test);

        // Execute each step sequentially
        this.steps.forEach((step: TestStep, index: number) => {
          cy.then(() => {
            // Create Step instance for execution
            const stepInstance = new Step(step);

            // Update current step in global state
            Cypress.sdt.current.step = stepInstance;

            // Store the step instance back in the test
            this.steps[index] = stepInstance;

            // Execute the step
            return stepInstance.run();
          });
        });
      } catch (error) {
        // Log test execution error
        console.error(`Test execution failed: ${this.description}`, error);

        // Add to tests with error if not already tracked
        if (!Cypress.sdt.results.testsWithError.includes(this)) {
          Cypress.sdt.results.testsWithError.push(this);
        }

        throw error;
      }
    });
  }
}
